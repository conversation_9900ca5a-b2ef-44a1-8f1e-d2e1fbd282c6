<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Sale Deed Form</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        form,
        .output {
            max-width: 600px;
            margin-top: 20px;
        }

        label {
            display: block;
            margin-top: 10px;
        }

        input {
            width: 100%;
            padding: 8px;
            margin-top: 5px;
        }

        button {
            margin-top: 15px;
            padding: 10px 20px;
        }

        .output {
            background: #f5f5f5;
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap;
        }
    </style>
</head>

<body>

    <h2>Sale Deed Form</h2>
    <form id="deedForm">
        <label>Full Name
            <input type="text" id="name" required>
        </label>
        <label>Father's Name
            <input type="text" id="father_name" required>
        </label>
        <label>Property Size (in sq.ft.)
            <input type="number" id="property_size" required>
        </label>
        <label>Sale Amount (₹)
            <input type="number" id="sale_amount" required>
        </label>
        <label>Date
            <input type="date" id="date" required>
        </label>
        <button type="submit">Generate Sale Deed</button>
    </form>

    <div class="output" id="deedOutput" style="display:none;"></div>
    <button id="downloadBtn" style="display:none;">Download as PDF</button>

    <script>
        const { jsPDF } = window.jspdf;

        document.getElementById('deedForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const name = document.getElementById('name').value;
            const fatherName = document.getElementById('father_name').value;
            const propertySize = document.getElementById('property_size').value;
            const saleAmount = document.getElementById('sale_amount').value;
            const date = document.getElementById('date').value;

            const template = `This Sale Deed is made on ${date} between ${name}, S/o ${fatherName}, for a property of ${propertySize} sq.ft., sold for ₹${saleAmount}.`;

            const outputDiv = document.getElementById('deedOutput');
            outputDiv.textContent = template;
            outputDiv.style.display = 'block';

            document.getElementById('downloadBtn').style.display = 'inline-block';
        });

        document.getElementById('downloadBtn').addEventListener('click', function () {
            const deedText = document.getElementById('deedOutput').textContent;
            const doc = new jsPDF();
            doc.text(deedText, 10, 20, { maxWidth: 180 });
            doc.save("sale_deed.pdf");
        });
    </script>

</body>

</html>